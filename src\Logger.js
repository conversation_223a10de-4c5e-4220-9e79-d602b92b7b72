"use strict";
console.log('========== Logger.js =================');

const winston = require('winston');
const { format } = winston;
const fs = require('fs');
const path = require('path');

// logging into RAM FIFO
class MemoryArray {   // remove winston for memory FIFO logging 
    constructor(maxSize = 100) {
        this.logArray = [];     // logArray in top level : constructor 
        this.maxSize = maxSize;
    }

    log(info) {
        if (this.logArray.length >= this.maxSize) {
            this.logArray.shift(); // FIFO: Remove oldest entry
        }
        // this.logArray.push(info);
        const logEntry = {
            timestamp: info.timestamp,
            level: info.level,
            message: info.message
        }
        if (info.data) { logEntry.data = info.data; }
        if (info.stack) { logEntry.stack = info.stack; }

        this.logArray.push(logEntry);
    }

    query() {
        return this.logArray;
    }

    reset() {
        this.logArray = [];
    }
    getLength() {
        return this.logArray.length;
    }
}

// Function to read last N lines from a file synchronously
function readLastLinesSync(filePath, n) {
    let bufferSize = 1024;
    const buffer = Buffer.alloc(bufferSize);
    let lines = [];
    let position = 0;
    let fd = fs.openSync(filePath, 'r');
    let stats = fs.statSync(filePath);
    let fileSize = stats.size;
    let bytesRead = 0;

    while (fileSize > 0 && lines.length < n) {
        position = fileSize - bufferSize;
        if (position < 0) {
            bufferSize += position;
            position = 0;
        }
        bytesRead = fs.readSync(fd, buffer, 0, bufferSize, position);
        let data = buffer.toString('utf8', 0, bytesRead);
        let parts = data.split('\n');
        if (parts.length > 1) {
            lines = parts.slice(0, -1).concat(lines);
            fileSize = position + parts[parts.length - 1].length;
        } else {
            fileSize = position;
        }
        if (lines.length >= n) {
            lines = lines.slice(-n);
            break;
        }
    }
    fs.closeSync(fd);
    return lines;
}

// Logger constructor 
function loggerConstructor(options = {}) {
    const {
        size = 100, // Max number of memory entries
        filename = 'logger', // Base filename 
        filesize = 5 * 1024 , // 5kb default file size
        persist = false, // Load from file on startup
        storeOnly = [], // Level to store in file
        logDirectory = './logs',
        verboseStack = true, // Show complete stack trace
        enableConsole = true // Log to console
    } = options;

    const memoryArray = new MemoryArray(size);

    const transports = [];

    if (filename) {
        transports.push(new winston.transports.File({
            filename: `${logDirectory}/${filename}.log`,
            maxsize: filesize,
            maxFiles: 2,
            format: format.combine(
                format.timestamp({ format: 'HH:mm:ss' }),
                format.printf((info) => {
                    const logObj = {
                        timestamp: info.timestamp,
                        level: info.level,
                        message: info.message
                    };

                    if (info.data) { logObj.data = info.data; }
                    if (info.stack) { logObj.stack = info.stack; }

                    return JSON.stringify(logObj);
                })
            ),
            level: storeOnly.includes('info') ? 'info' : storeOnly.includes('warn') ? 'warn' : storeOnly.includes('error') ? 'error' : 'info',    // default revert to info/// array of allowable things : dict of allowable things 
        }))
    }

    if (enableConsole) {
        transports.push(new winston.transports.Console({
            level: 'info',
            format: format.combine(
                format.colorize(),
                format.timestamp({ format: 'HH:mm:ss' }),
                format.printf(({ timestamp, level, message, data, stack }) => {
                    let output = `${timestamp} [${level}] ${message}`;
                    if (data) output += ` | Data: ${data}`;
                    if (stack) output += `\n   Error: ${stack}`;
                    return output;
                })
            ),
        }));
    }

    const logger = transports.length > 0 ? winston.createLogger({ transports }) : null;

    return {
        init() {
            if (persist && filename) {
                try {
                    const fileCurrent = path.join(logDirectory, `${filename}.log`);
                    const fileOld = path.join(logDirectory, `${filename}.1.log`);
                    let lines = [];

                    if (fs.existsSync(fileCurrent)) {
                        lines = readLastLinesSync(fileCurrent, size);
                    }
                    // If we need more lines and the rotated file exists, read from it
                    if (lines.length < size && fs.existsSync(fileOld)) {
                        const remaining = size - lines.length;
                        const linesOld = readLastLinesSync(fileOld, remaining);
                        lines = linesOld.concat(lines); // Older lines first, then newer
                    }

                    const entries = lines
                        .filter(line => line.trim() !== '')
                        .map(line => {
                            try {
                                return JSON.parse(line);
                            } catch (error) {
                                console.error("Error parsing log :", error);
                                return null;
                            }
                        })
                        .filter(entry => entry !== null);
                    memoryArray.logArray = entries;
                } catch (error) {
                    console.error("Error loading log file:", error);
                }
            }
        },
        _log(level, msg, data) {
            let logEntry = logFormat(msg, data, level, verboseStack);
            memoryArray.log(logEntry);
            if (logger) {
                logger.log(logEntry);
            }
        },
        log(msg, data) {
            this._log('info', msg, data);
        },
        warn(msg, data) {
            this._log('warn', msg, data);
        },
        error(msg, data) {
            this._log('error', msg, data);
        },
        getLogs() {
            return memoryArray.query();
        },
        reset() {
            memoryArray.reset();
        },
        query(indexed = 0, offset = 0) {
            return memoryArray.query().slice(indexed, indexed + offset || undefined);
        },
    };
}

// Log format 
function logFormat(msg, data, level = 'error', verboseStack = false) {
    const r = {
        timestamp: Date.now(),
        level,
    };

    if (typeof msg === 'string') {
        r.message = msg;
    } else if (msg instanceof Error) {
        r.message = msg.message;
        if (verboseStack) { r.stack = msg.stack; }
        else { r.stack = msg.stack.split('\n').slice(0, 2).join('\n'); }
    }

    if (data !== undefined) {
        r.data = data;
    }
    return r;
}

// loggers
const startupLogger = loggerConstructor({
    size: 100,
    filename: 'startup',
    filesize: 5 * 1024,
    persist: true,
    storeOnly: ['error', 'warn', 'info'],
    verboseStack: false,
    enableConsole: true,
});

const systemLogger = loggerConstructor({
    size: 100,
    filename: 'system',
    filesize: 10 * 1024 * 1024,
});

const alertLogger = loggerConstructor({
    size: 100,
    filename: 'alerts',
    filesize: 10 * 1024 * 1024,
});

module.exports = {
    startupLogger,
    systemLogger,
    alertLogger,
    logFormat,
};

// usage
// startupLogger.log('System started');
// const object = { a: 1, b: 2, c: 3 };
// startupLogger.warn('warning message with object', object);
// startupLogger.error(new Error('Test error'));


// test case 

// startupLogger.init();
// console.log("before logging stuff",startupLogger.getLogs());

// startupLogger.log('System started mama mia');
// startupLogger.warn('warning message with object', { a: 1, b: 2, c: 3 });
// startupLogger.error(new Error('Test error'));

// console.log("after logging stuff",startupLogger.getLogs());


const testLogger = loggerConstructor({
    size: 100,
    filename: 'test-rotation',
    filesize: 2 * 1024, // B 
    persist: false,
    storeOnly: ['info'],
    verboseStack: false,
    enableConsole: true,
});

testLogger.log(`mama mia : whoooooooooooooooooo`);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia `);
testLogger.log(`mama mia logg in 2.log`);

// Check if files were created
// console.log('Files in logs directory:', fs.readdirSync('./logs'));